[debug] [2025-07-28T18:19:40.403Z] ----------------------------------------------------------------------
[debug] [2025-07-28T18:19:40.405Z] Command:       /usr/local/bin/node /usr/local/bin/firebase emulators:start --only functions
[debug] [2025-07-28T18:19:40.405Z] CLI Version:   14.9.0
[debug] [2025-07-28T18:19:40.405Z] Platform:      darwin
[debug] [2025-07-28T18:19:40.405Z] Node Version:  v24.3.0
[debug] [2025-07-28T18:19:40.405Z] Time:          Mon Jul 28 2025 20:19:40 GMT+0200 (Central European Summer Time)
[debug] [2025-07-28T18:19:40.405Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-28T18:19:40.482Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-28T18:19:40.482Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[warn] ⚠  hub: emulator hub unable to start on port 4400, starting on 4401 instead. {"metadata":{"emulator":{"name":"hub"},"message":"emulator hub unable to start on port 4400, starting on 4401 instead."}}
[warn] ⚠  ui: Emulator UI unable to start on port 4000, starting on 4001 instead. {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI unable to start on port 4000, starting on 4001 instead."}}
[warn] ⚠  logging: Logging Emulator unable to start on port 4500, starting on 4501 instead. {"metadata":{"emulator":{"name":"logging"},"message":"Logging Emulator unable to start on port 4500, starting on 4501 instead."}}
[debug] [2025-07-28T18:19:40.973Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-28T18:19:40.973Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4401},{"address":"::1","family":"IPv6","port":4401}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4001},{"address":"::1","family":"IPv6","port":4001}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4501}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-28T18:19:40.974Z] [hub] writing locator at /var/folders/w4/78n4kwrn7193_tdy71jvvk0r0000gp/T/hub-progressorhq-stars-td-internal.json
[debug] [2025-07-28T18:19:41.402Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] ⚠  eventarc: Eventarc Emulator unable to start on port 9299, starting on 9300 instead. {"metadata":{"emulator":{"name":"eventarc"},"message":"Eventarc Emulator unable to start on port 9299, starting on 9300 instead."}}
[debug] [2025-07-28T18:19:41.402Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] ⚠  tasks: Cloud Tasks Emulator unable to start on port 9499, starting on 9500 instead. {"metadata":{"emulator":{"name":"tasks"},"message":"Cloud Tasks Emulator unable to start on port 9499, starting on 9500 instead."}}
[debug] [2025-07-28T18:19:41.402Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-28T18:19:41.402Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4401},{"address":"::1","family":"IPv6","port":4401}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4001},{"address":"::1","family":"IPv6","port":4001}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4501}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5002}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9300}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9500}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-28T18:19:41.406Z] defaultcredentials: writing to file /Users/<USER>/.config/firebase/dmitry_kuzmin_teamdev.com_application_default_credentials.json
[debug] [2025-07-28T18:19:41.407Z] Setting GAC to /Users/<USER>/.config/firebase/dmitry_kuzmin_teamdev.com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to /Users/<USER>/.config/firebase/dmitry_kuzmin_teamdev.com_application_default_credentials.json"}}
[debug] [2025-07-28T18:19:41.407Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:41.407Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:41.408Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/progressorhq-stars-td-internal/adminSdkConfig [none]
[debug] [2025-07-28T18:19:41.696Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/progressorhq-stars-td-internal/adminSdkConfig 200
[debug] [2025-07-28T18:19:41.696Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/progressorhq-stars-td-internal/adminSdkConfig {"projectId":"progressorhq-stars-td-internal","storageBucket":"progressorhq-stars-td-internal.firebasestorage.app"}
[info] i  functions: Watching "/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions\" for Cloud Functions..."}}
[debug] [2025-07-28T18:19:41.716Z] Validating nodejs source
[warn] ⚠  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[debug] [2025-07-28T18:19:41.969Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "The Progressor MCP Server",
  "scripts": {
    "build": "npx buf generate ../../model/proto --debug && tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "npm run build && firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "@bufbuild/buf": "^1.55.1",
    "@bufbuild/protobuf": "^1.10.1",
    "@bufbuild/protoc-gen-es": "^1.10.1",
    "@modelcontextprotocol/sdk": "^1.16.0",
    "@ngrok/ngrok": "^1.5.1",
    "@types/express": "^4.17.23",
    "axios": "^1.11.0",
    "express": "^4.21.2",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "private": true
}
[debug] [2025-07-28T18:19:41.969Z] Building nodejs source
[debug] [2025-07-28T18:19:41.969Z] Failed to find version of module node: reached end of search path /Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules
[warn] ⚠  functions: Your requested "node" version "22" doesn't match your global version "24". Using node@24 from host. 
[debug] [2025-07-28T18:19:41.970Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-28T18:19:41.973Z] Found firebase-functions binary at '/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8727

[debug] [2025-07-28T18:19:42.093Z] Got response from /__/functions.yaml {"endpoints":{"mcp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":5,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["us-central1"],"secretEnvironmentVariables":[{"key":"PROGRESSOR_STARS_MCP_SECRET_KEY"},{"key":"PROGRESSOR_STARS_NGROK_AUTHTOKEN"}],"labels":{},"httpsTrigger":{},"entryPoint":"mcp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{},"params":[{"type":"secret","name":"PROGRESSOR_STARS_MCP_SECRET_KEY"},{"type":"secret","name":"PROGRESSOR_STARS_NGROK_AUTHTOKEN"}]}
[info] ✔  functions: Loaded functions definitions from source: mcp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: mcp."}}
[info] ✔  functions[us-central1-mcp]: http function initialized (http://127.0.0.1:5002/progressorhq-stars-td-internal/us-central1/mcp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5002/progressorhq-stars-td-internal/us-central1/mcp)."}}
[debug] [2025-07-28T18:19:46.106Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4001/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5002 │ http://127.0.0.1:4001/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4401
  Other reserved ports: 4501

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-28T18:19:57.579Z] [work-queue] {"queuedWork":["/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token-2025-07-28T18:19:57.579Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-28T18:19:57.580Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token-2025-07-28T18:19:57.579Z"],"workRunningCount":1}
[debug] [2025-07-28T18:19:57.580Z] Accepted request OPTIONS /progressorhq-stars-td-internal/us-central1/mcp/encrypt-token --> us-central1-mcp
[debug] [2025-07-28T18:19:57.581Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-28T18:19:57.581Z] [functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token, mapping to path=/encrypt-token {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token, mapping to path=/encrypt-token"}}
[info] i  functions: Trying to access secret PROGRESSOR_STARS_MCP_SECRET_KEY@latest {"metadata":{"emulator":{"name":"functions"},"message":"Trying to access secret PROGRESSOR_STARS_MCP_SECRET_KEY@latest"}}
[debug] [2025-07-28T18:19:57.583Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:57.583Z] Checked if tokens are valid: true, expires at: 1753729664909
[info] i  functions: Trying to access secret PROGRESSOR_STARS_NGROK_AUTHTOKEN@latest {"metadata":{"emulator":{"name":"functions"},"message":"Trying to access secret PROGRESSOR_STARS_NGROK_AUTHTOKEN@latest"}}
[debug] [2025-07-28T18:19:57.583Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:57.583Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:57.583Z] >>> [apiv2][query] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access [none]
[debug] [2025-07-28T18:19:57.585Z] >>> [apiv2][query] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access [none]
[debug] [2025-07-28T18:19:57.774Z] <<< [apiv2][status] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access 200
[debug] [2025-07-28T18:19:57.774Z] <<< [apiv2][body] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access {"name":"projects/534333428798/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/1","payload":{"data":"NjYzMUEyNjYzODYyREM5QQ==","dataCrc32c":"2064910593"}}
[debug] [2025-07-28T18:19:57.807Z] <<< [apiv2][status] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access 200
[debug] [2025-07-28T18:19:57.807Z] <<< [apiv2][body] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access {"name":"projects/534333428798/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/1","payload":{"data":"MnpzNjBoUXdRblBxOGJHTzQyN1NQNnlqeXFIXzdxQXp1enllbnQyREZ2YThLa3M5QQ==","dataCrc32c":"45872750"}}
[debug] [2025-07-28T18:19:57.811Z] [worker-pool] addWorker(us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-mcp)"}}
[debug] [2025-07-28T18:19:57.812Z] [worker-pool] Adding worker with key us-central1-mcp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-mcp, total=1"}}
[debug] [2025-07-28T18:19:58.056Z] [runtime-status] [22274] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}"}}
[debug] [2025-07-28T18:19:58.056Z] [runtime-status] [22274] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.056Z] [runtime-status] [22274] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-28T18:19:58.056Z] [runtime-status] [22274] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.114Z] [runtime-status] [22274] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-28T18:19:58.114Z] [runtime-status] [22274] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}}"}}
[debug] [2025-07-28T18:19:58.114Z] [runtime-status] [22274] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.115Z] [runtime-status] [22274] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}"}}
[debug] [2025-07-28T18:19:58.116Z] [runtime-status] [22274] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.116Z] [runtime-status] [22274] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}}"}}
[debug] [2025-07-28T18:19:58.157Z] [runtime-status] [22274] Functions runtime initialized. {"cwd":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions","node_version":"24.3.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Functions runtime initialized. {\"cwd\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions\",\"node_version\":\"24.3.0\"}"}}
[debug] [2025-07-28T18:19:58.157Z] [runtime-status] [22274] Listening to port: /var/folders/w4/78n4kwrn7193_tdy71jvvk0r0000gp/T/fire_emu_06e1e90ee25c67c5.sock {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22274] Listening to port: /var/folders/w4/78n4kwrn7193_tdy71jvvk0r0000gp/T/fire_emu_06e1e90ee25c67c5.sock"}}
[debug] [2025-07-28T18:19:58.222Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE"}}
[debug] [2025-07-28T18:19:58.222Z] [worker-pool] submitRequest(triggerId=us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-mcp)"}}
[info] i  functions: Beginning execution of "us-central1-mcp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Beginning execution of \"us-central1-mcp\""}}
[debug] [2025-07-28T18:19:58.222Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY"}}
[debug] [2025-07-28T18:19:58.224Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-mcp" in 1.902125ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finished \"us-central1-mcp\" in 1.902125ms"}}
[debug] [2025-07-28T18:19:58.224Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE"}}
[debug] [2025-07-28T18:19:58.224Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-28T18:19:58.225Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-28T18:19:58.225Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-28T18:19:58.226Z] [work-queue] {"queuedWork":["/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token-2025-07-28T18:19:58.226Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-28T18:19:58.226Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token-2025-07-28T18:19:58.226Z"],"workRunningCount":1}
[debug] [2025-07-28T18:19:58.226Z] Accepted request POST /progressorhq-stars-td-internal/us-central1/mcp/encrypt-token --> us-central1-mcp
[debug] [2025-07-28T18:19:58.226Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-28T18:19:58.226Z] [functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token, mapping to path=/encrypt-token {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/encrypt-token, mapping to path=/encrypt-token"}}
[debug] [2025-07-28T18:19:58.226Z] [worker-pool] submitRequest(triggerId=us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-mcp)"}}
[info] i  functions: Beginning execution of "us-central1-mcp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Beginning execution of \"us-central1-mcp\""}}
[debug] [2025-07-28T18:19:58.226Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY"}}
[debug] [2025-07-28T18:19:58.256Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-mcp" in 29.275917ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finished \"us-central1-mcp\" in 29.275917ms"}}
[debug] [2025-07-28T18:19:58.256Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE"}}
[debug] [2025-07-28T18:19:58.256Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-28T18:19:58.256Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-28T18:19:58.256Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-28T18:19:58.264Z] [work-queue] {"queuedWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.264Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-28T18:19:58.264Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.264Z"],"workRunningCount":1}
[debug] [2025-07-28T18:19:58.264Z] Accepted request GET /progressorhq-stars-td-internal/us-central1/mcp/publicUrl --> us-central1-mcp
[debug] [2025-07-28T18:19:58.264Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-28T18:19:58.264Z] [functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/publicUrl, mapping to path=/publicUrl {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/publicUrl, mapping to path=/publicUrl"}}
[debug] [2025-07-28T18:19:58.264Z] [worker-pool] submitRequest(triggerId=us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-mcp)"}}
[info] i  functions: Beginning execution of "us-central1-mcp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Beginning execution of \"us-central1-mcp\""}}
[debug] [2025-07-28T18:19:58.264Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY"}}
[info] >  {"severity":"INFO","message":"No existing host found, opening new tunnel..."} {"user":{"severity":"INFO","message":"No existing host found, opening new tunnel..."},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"severity\":\"INFO\",\"message\":\"No existing host found, opening new tunnel...\"}"}}
[info] >  {"severity":"INFO","message":"Starting ngrok tunnel on port 5002 with domain mcp-server-0358c4de.ngrok.app..."} {"user":{"severity":"INFO","message":"Starting ngrok tunnel on port 5002 with domain mcp-server-0358c4de.ngrok.app..."},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"severity\":\"INFO\",\"message\":\"Starting ngrok tunnel on port 5002 with domain mcp-server-0358c4de.ngrok.app...\"}"}}
[debug] [2025-07-28T18:19:58.266Z] [work-queue] {"queuedWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.266Z"],"queueLength":1,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.264Z"],"workRunningCount":1}
[debug] [2025-07-28T18:19:58.266Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.264Z","/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.266Z"],"workRunningCount":2}
[debug] [2025-07-28T18:19:58.267Z] Accepted request GET /progressorhq-stars-td-internal/us-central1/mcp/publicUrl --> us-central1-mcp
[debug] [2025-07-28T18:19:58.267Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-28T18:19:58.267Z] [functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/publicUrl, mapping to path=/publicUrl {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/progressorhq-stars-td-internal/us-central1/mcp/publicUrl, mapping to path=/publicUrl"}}
[info] i  functions: Trying to access secret PROGRESSOR_STARS_MCP_SECRET_KEY@latest {"metadata":{"emulator":{"name":"functions"},"message":"Trying to access secret PROGRESSOR_STARS_MCP_SECRET_KEY@latest"}}
[debug] [2025-07-28T18:19:58.267Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:58.267Z] Checked if tokens are valid: true, expires at: 1753729664909
[info] i  functions: Trying to access secret PROGRESSOR_STARS_NGROK_AUTHTOKEN@latest {"metadata":{"emulator":{"name":"functions"},"message":"Trying to access secret PROGRESSOR_STARS_NGROK_AUTHTOKEN@latest"}}
[debug] [2025-07-28T18:19:58.267Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:58.267Z] Checked if tokens are valid: true, expires at: 1753729664909
[debug] [2025-07-28T18:19:58.267Z] >>> [apiv2][query] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access [none]
[debug] [2025-07-28T18:19:58.267Z] >>> [apiv2][query] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access [none]
[debug] [2025-07-28T18:19:58.374Z] <<< [apiv2][status] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access 200
[debug] [2025-07-28T18:19:58.375Z] <<< [apiv2][body] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/latest:access {"name":"projects/534333428798/secrets/PROGRESSOR_STARS_NGROK_AUTHTOKEN/versions/1","payload":{"data":"MnpzNjBoUXdRblBxOGJHTzQyN1NQNnlqeXFIXzdxQXp1enllbnQyREZ2YThLa3M5QQ==","dataCrc32c":"45872750"}}
[debug] [2025-07-28T18:19:58.415Z] <<< [apiv2][status] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access 200
[debug] [2025-07-28T18:19:58.415Z] <<< [apiv2][body] GET https://secretmanager.googleapis.com/v1/projects/progressorhq-stars-td-internal/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/latest:access {"name":"projects/534333428798/secrets/PROGRESSOR_STARS_MCP_SECRET_KEY/versions/1","payload":{"data":"NjYzMUEyNjYzODYyREM5QQ==","dataCrc32c":"2064910593"}}
[debug] [2025-07-28T18:19:58.416Z] [worker-pool] addWorker(us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(us-central1-mcp)"}}
[debug] [2025-07-28T18:19:58.416Z] [worker-pool] Adding worker with key us-central1-mcp, total=2 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key us-central1-mcp, total=2"}}
[debug] [2025-07-28T18:19:58.645Z] [runtime-status] [22275] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}"}}
[debug] [2025-07-28T18:19:58.646Z] [runtime-status] [22275] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.646Z] [runtime-status] [22275] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-28T18:19:58.646Z] [runtime-status] [22275] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.700Z] [runtime-status] [22275] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-28T18:19:58.700Z] [runtime-status] [22275] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}}"}}
[debug] [2025-07-28T18:19:58.700Z] [runtime-status] [22275] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.701Z] [runtime-status] [22275] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}"}}
[debug] [2025-07-28T18:19:58.701Z] [runtime-status] [22275] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.3.2","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.3.2\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-functions/lib/v2/index.js\"}"}}
[debug] [2025-07-28T18:19:58.701Z] [runtime-status] [22275] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions/node_modules/firebase-admin/lib/index.js\"}}"}}
[debug] [2025-07-28T18:19:58.741Z] [runtime-status] [22275] Functions runtime initialized. {"cwd":"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions","node_version":"24.3.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Functions runtime initialized. {\"cwd\":\"/Users/<USER>/IdeaProjects/v2-Stars/mcp-server/functions\",\"node_version\":\"24.3.0\"}"}}
[debug] [2025-07-28T18:19:58.741Z] [runtime-status] [22275] Listening to port: /var/folders/w4/78n4kwrn7193_tdy71jvvk0r0000gp/T/fire_emu_cfd7414986ed4596.sock {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[runtime-status] [22275] Listening to port: /var/folders/w4/78n4kwrn7193_tdy71jvvk0r0000gp/T/fire_emu_cfd7414986ed4596.sock"}}
[info] >  {"severity":"INFO","message":"Tunnel opened, host: mcp-server-0358c4de.ngrok.app"} {"user":{"severity":"INFO","message":"Tunnel opened, host: mcp-server-0358c4de.ngrok.app"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"severity\":\"INFO\",\"message\":\"Tunnel opened, host: mcp-server-0358c4de.ngrok.app\"}"}}
[debug] [2025-07-28T18:19:58.824Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-mcp" in 559.511041ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finished \"us-central1-mcp\" in 559.511041ms"}}
[debug] [2025-07-28T18:19:58.824Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE"}}
[debug] [2025-07-28T18:19:58.824Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-28T18:19:58.824Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-28T18:19:58.824Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/progressorhq-stars-td-internal/us-central1/mcp/publicUrl-2025-07-28T18:19:58.266Z"],"workRunningCount":1}
[debug] [2025-07-28T18:19:58.825Z] [worker-us-central1-mcp-4acd7044-df73-440b-89f0-e74facd55b5b]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-4acd7044-df73-440b-89f0-e74facd55b5b]: IDLE"}}
[debug] [2025-07-28T18:19:58.825Z] [worker-pool] submitRequest(triggerId=us-central1-mcp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=us-central1-mcp)"}}
[info] i  functions: Beginning execution of "us-central1-mcp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Beginning execution of \"us-central1-mcp\""}}
[debug] [2025-07-28T18:19:58.825Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: BUSY"}}
[info] >  {"severity":"INFO","message":"Returning existing host: mcp-server-0358c4de.ngrok.app"} {"user":{"severity":"INFO","message":"Returning existing host: mcp-server-0358c4de.ngrok.app"},"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"\u001b[90m> \u001b[39m {\"severity\":\"INFO\",\"message\":\"Returning existing host: mcp-server-0358c4de.ngrok.app\"}"}}
[debug] [2025-07-28T18:19:58.826Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "us-central1-mcp" in 0.777458ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finished \"us-central1-mcp\" in 0.777458ms"}}
[debug] [2025-07-28T18:19:58.826Z] [worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"[worker-us-central1-mcp-67c1052f-dbd2-4493-9890-ab0e1e4e1633]: IDLE"}}
[debug] [2025-07-28T18:19:58.826Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-28T18:19:58.826Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"us-central1-mcp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-28T18:19:58.826Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
