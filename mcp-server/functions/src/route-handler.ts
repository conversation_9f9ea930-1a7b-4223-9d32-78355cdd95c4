/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import * as express from "express";

/**
 * A provider function that returns a secret value.
 */
export type SecretProvider = () => string;

/**
 * Information about a secret required by a handler.
 */
export interface SecretInfo {
    name: string;
    provider: SecretProvider;
}

/**
 * Base class for handlers that handle specific sub-routes
 * within the `mcpServer` Firebase Function.
 */
export abstract class RouteHandler {

    protected readonly route: string;

    constructor(route: string) {
        this.route = route;
    }

    /**
     * Returns the HTTP methods that this handler supports.
     */
    abstract methods(): string[];

    /**
     * Handles the actual request processing logic.
     *
     * @param req the Express request object.
     * @param res the Express response object.
     */
    abstract doHandle(req: express.Request, res: express.Response): void | Promise<void>;

    /**
     * Returns the list of secrets required by this handler.
     */
    get secrets(): SecretInfo[] {
        return [];
    }

    /**
     * Registers this route handler with the Express application.
     *
     * @param app the Express application to register the route with.
     */
    registerRoute(app: express.Application): void {
        this.methods().forEach(method => {
            const methodLower = method.toLowerCase();
            (app as any)[methodLower](this.route, this.doHandle.bind(this));
        });
    }
}
