/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {defineSecret} from "firebase-functions/params";
import {RouteHandler} from "../route-handler";
import {McpServer, serverInfo} from "./mcp-server";
import * as express from "express";
import {JSONRPCRequest} from "@modelcontextprotocol/sdk/types.js";
import * as logger from "firebase-functions/logger";
import {IncomingHttpHeaders} from "http";
import {SecretParam} from "firebase-functions/lib/params/types";

const mcpServerSecretKey = defineSecret("PROGRESSOR_STARS_MCP_SECRET_KEY");

/**
 * Handles the requests from LLMs according to the MCP protocol.
 */
export class McpHand<PERSON> extends RouteHandler {

    private readonly mcpServer: McpServer;

    constructor(route: string) {
        super(route);
        this.mcpServer = new McpServer(() => mcpServerSecretKey.value());
    }

    methods(): string[] {
        return ["all"];
    }

    async doHandle(req: express.Request, res: express.Response): Promise<void> {
        try {
            if (req.method === "GET") {
                res.json({
                    message: "MCP Server endpoint",
                    protocols: ["Streamable HTTP"],
                    server: serverInfo
                });
                return;
            }
            const mcpRequest = req.body as JSONRPCRequest;
            logger.info(`--- [MCP] Handling request: ${JSON.stringify(mcpRequest)}.`);
            this.populateMetadata(mcpRequest, req.headers);
            logger.info(`--- [MCP] Handling request: ${JSON.stringify(mcpRequest)}.`);
            const mcpResponse = await this.mcpServer.handleRequest(mcpRequest);
            logger.info(`--- [MCP] Sending response: ${JSON.stringify(mcpResponse)}.`);
            res.json(mcpResponse);
        } catch (error) {
            logger.error("Error handling the MCP request:", error);
            res.status(500).json({
                jsonrpc: "2.0",
                id: null,
                error: {
                    code: -32603,
                    message: "Internal error",
                    data: error instanceof Error ? error.message : "Unknown error"
                }
            });
        }
    }

    private populateMetadata(mcpRequest: JSONRPCRequest, headers: IncomingHttpHeaders) {
        const metaHeader = headers["x-meta"] as string;
        if (metaHeader) {
            try {
                if (!mcpRequest.params) {
                    mcpRequest.params = {};
                }
                mcpRequest.params!._meta = JSON.parse(metaHeader);
                logger.info(`Extracted metadata from the header: ${metaHeader}.`);
            } catch (error) {
                logger.error("Failed to parse the 'x-meta' header:", error);
            }
        }
    }

    get secrets(): SecretParam[] {
        return [mcpServerSecretKey];
    }
}
