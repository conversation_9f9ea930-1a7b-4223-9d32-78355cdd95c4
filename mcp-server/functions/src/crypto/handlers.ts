/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import {<PERSON><PERSON><PERSON><PERSON>, SecretInfo, SecretProvider} from "../route-handler";
import {Crypto} from "./crypto";
import * as express from "express";
import * as logger from "firebase-functions/logger";

/**
 * Encrypts the Progressor token using the server's secret key.
 *
 * This endpoint allows the frontend to get an encrypted token
 * without knowing the secret key.
 */
export class EncryptTokenHandler extends RouteHandler {

    private readonly crypto: Crypto;
    private readonly secretProvider: SecretProvider;

    constructor(route: string, secretProvider: SecretProvider) {
        super(route);
        this.secretProvider = secretProvider;
        this.crypto = new Crypto(secretProvider);
    }

    methods(): string[] {
        return ["post"];
    }

    async doHandle(req: express.Request, res: express.Response): Promise<void> {
        try {
            const {token} = req.body;
            if (!token || typeof token !== "string") {
                res.status(400).json({
                    error: "The token is required and must be a string."
                });
                return;
            }
            const encryptedToken = this.crypto.encrypt(token);
            res.json({
                encryptedToken: encryptedToken
            });
        } catch (error) {
            logger.error("Error encrypting the token:", error);
            res.status(500).json({
                error: "Failed to encrypt the token."
            });
        }
    }

    get secrets(): SecretParam[] {
        return [mcpServerSecretKey];
    }
}
