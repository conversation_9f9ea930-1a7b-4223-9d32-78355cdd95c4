/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

/**
 * The entry point for the Progressor MCP Server hosted on Firebase Functions.
 *
 * The MCP server enables the Progressor app's integration with generative AI models
 * by handling the requests from LLMs to fetch data from the Progressor Mothership
 * and perform actions on behalf of the user.
 *
 * For more information, see https://modelcontextprotocol.io.
 */

import {onRequest} from "firebase-functions/https";
import express from "express";
import {SecretParam} from "firebase-functions/lib/params/types";
import {HealthHandler, ServerInfoHandler} from "./handlers";
import {EncryptTokenHandler} from "./crypto/handlers";
import {McpHandler} from "./mcp/handlers";
import {PublicUrlHandler} from "./ngrok/handlers";

/**
 * Configuration constants for the Firebase Functions deployment.
 */
const region = "us-central1";

/**
 * The Express app that handles requests to the MCP server.
 */
const app = express();
app.use(express.json());

/**
 * Register all route handlers with the Express app.
 */
const handlers = [
    new ServerInfoHandler("/"),
    new HealthHandler("/health"),
    new EncryptTokenHandler("/encrypt-token"),
    new PublicUrlHandler("/publicUrl"),
    new McpHandler("/mcp")
];

handlers.forEach(handler => handler.registerWith(app));

const secrets = handlers
    .flatMap(handler => handler.secrets)
    .reduce((map, secret) => {
        if (!map.has(secret.name)) {
            map.set(secret.name, secret);
        }
        return map;
    }, new Map<string, SecretParam>())
    .values();

export const mcp = onRequest({
    maxInstances: 5,
    region: region,
    secrets: [...secrets]
}, app);
